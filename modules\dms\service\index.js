/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.dms.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '../../../src/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();

    const service = {
        // 示例注释代码，请在添加接口后删除
        /*
        getList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/xxx/xxx/xxx',
                method: 'get',
                params: query
            });
        }
        */
    };

    return service;
};
