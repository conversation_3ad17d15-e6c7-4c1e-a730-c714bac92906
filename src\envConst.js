const getWayList = [
    // 网关列表 包含该网关下的服务列表。使用时根据网关+服务来使用
    {
        getWayKey: 'systemApi',
        getWayValue: '',
        host: '',
        services: {
            system: ''
        }
    }
];
// 服务根url对象，包含：根域名+网关+服务
let basePath = null;

/**
 * 获取当前环境所在的基本url地址
 * @returns {string} 环境对应的基础URL地址
 */
const getEnvUrl = () => {
    const { hostname } = window.location;

    // 根据不同环境（本地/测试/生产），使用不同地址
    if (hostname === '127.0.0.1' || hostname === 'local-plouto.xtjc.net' || hostname === 'test-plouto.xtjc.net') {
        // 测试环境
        return 'test-plouto.xtjc.net';
    }
    // 生产环境
    return 'plouto.xtjc.net';
};

// 初始化服务路径，规则是取得当前url路径，解析出host，然后拼接网关，并存储到对象里。获取时：xxx.网关名称.服务名称
const initBaseUrl = (basePaths) => {
    const basePathsObj = {};
    basePaths.map((path) => {
        basePathsObj[path.getWayKey] = {};
        for (const serviceName in path.services) {
            if (Object.hasOwnProperty.call(path.services, serviceName)) {
                basePathsObj[path.getWayKey][serviceName] = `http://${getEnvUrl()}/api`;
            }
        }
        return path;
    });

    basePath = basePathsObj;

    return basePath;
};

// 构造函数，参数可以传getWay数组进来，会追加到变量getWayList里
const basePathInit = (list) => {
    if (list && Array.isArray(list) && list.length > 0) {
        for (let index = 0; index < list.length; index++) {
            const element = list[index];
            if (element) {
                getWayList.push(element);
            }
        }
    }
    return initBaseUrl(getWayList);
};

export { basePathInit };
